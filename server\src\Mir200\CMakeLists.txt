# Mir200 - Legend of Mir Private Server Core Engine
# Phase 1 Implementation - Following Original Delphi Project Logic

cmake_minimum_required(VERSION 3.16)
project(Mir200 VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Compiler flags
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4 /EHsc")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /DNDEBUG")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -pedantic")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")
endif()

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/Common
    ${CMAKE_CURRENT_SOURCE_DIR}/Network
    ${CMAKE_CURRENT_SOURCE_DIR}/Objects
    ${CMAKE_CURRENT_SOURCE_DIR}/Engine
    ${CMAKE_CURRENT_SOURCE_DIR}/Database
    ${CMAKE_CURRENT_SOURCE_DIR}/Script
)

# Source files - Core Common
set(COMMON_SOURCES
    Common/Types.h
    Common/Protocol.h
    Common/M2Share.h
    Common/M2Share.cpp
    Common/Grobal2.h
    Common/Grobal2.cpp
    Common/HUtil32.h
    Common/HUtil32.cpp
    Common/MudUtil.h
    Common/MudUtil.cpp
)

# Source files - Network
set(NETWORK_SOURCES
    Network/RunSocket.h
    Network/RunSocket.cpp
    Network/GateSocket.h
    Network/GateSocket.cpp
)

# Source files - Objects (Following original ObjBase.pas structure)
set(OBJECTS_SOURCES
    Objects/BaseObject.h
    Objects/BaseObject.cpp
    Objects/PlayObject.h
    Objects/PlayObject.cpp
    Objects/NormNpc.h
    Objects/NormNpc.cpp
    Objects/Merchant.h
    Objects/Merchant.cpp
    Objects/Monster.h
    Objects/Monster.cpp
    Objects/Guard.h
    Objects/Guard.cpp
)

# Source files - Engine (Following original UsrEngn.pas structure)
set(ENGINE_SOURCES
    Engine/UserEngine.h
    Engine/UserEngine.cpp
    Engine/Environment.h
    Engine/Environment.cpp
    Engine/LocalDatabase.h
    Engine/LocalDatabase.cpp
    Engine/Magic.h
    Engine/Magic.cpp
    Engine/Guild.h
    Engine/Guild.cpp
    Engine/Castle.h
    Engine/Castle.cpp
)

# Source files - Database
set(DATABASE_SOURCES
    Database/RunDB.h
    Database/RunDB.cpp
)

# Source files - Script
set(SCRIPT_SOURCES
    Script/Event.h
    Script/Event.cpp
    Script/Mission.h
    Script/Mission.cpp
)

# Main executable
set(MAIN_SOURCES
    main.cpp
    M2Server.h
    M2Server.cpp
)

# All sources
set(ALL_SOURCES
    ${COMMON_SOURCES}
    ${NETWORK_SOURCES}
    ${OBJECTS_SOURCES}
    ${ENGINE_SOURCES}
    ${DATABASE_SOURCES}
    ${SCRIPT_SOURCES}
    ${MAIN_SOURCES}
)

# Create executable
add_executable(Mir200 ${ALL_SOURCES})

# Link libraries
if(WIN32)
    target_link_libraries(Mir200 ws2_32 winmm)
endif()

# Set output directory
set_target_properties(Mir200 PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Create build directory structure
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/logs)
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/data)
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/config)

# Copy configuration files
configure_file(${CMAKE_CURRENT_SOURCE_DIR}/config/M2Server.ini ${CMAKE_BINARY_DIR}/config/M2Server.ini COPYONLY)

# Install target
install(TARGETS Mir200 DESTINATION bin)
