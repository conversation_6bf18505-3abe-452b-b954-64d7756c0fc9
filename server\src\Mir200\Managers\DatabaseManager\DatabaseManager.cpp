#include "DatabaseManager.h"
#include "../../Core/EventBus.h"
#include "Common/Types.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <filesystem>
#include <algorithm>
#include <chrono>

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <direct.h>
#else
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#endif

namespace Mir200 {

// Static helper functions
static DWORD GetCurrentTime() {
    return MirServer::GetCurrentTime();
}

static std::string GetCacheKey(const std::string& account, const std::string& charName) {
    return account + ":" + charName;
}

// =============== DatabaseManager Implementation ===============
DatabaseManager::DatabaseManager() 
    : BaseManager("DatabaseManager") {
}

DatabaseManager::~DatabaseManager() {
    Finalize();
}

bool DatabaseManager::Initialize() {
    std::cout << "Initializing DatabaseManager..." << std::endl;
    
    // Load default configuration
    LoadDBConfig("config/database.conf");
    
    // Ensure directories exist
    if (!EnsureDirectoryExists(m_config.dataPath) ||
        !EnsureDirectoryExists(m_config.humPath) ||
        !EnsureDirectoryExists(m_config.backupPath) ||
        !EnsureDirectoryExists(m_config.logPath)) {
        std::cerr << "Failed to create required directories" << std::endl;
        return false;
    }
    
    // Start worker thread
    m_workerRunning = true;
    m_workerThread = std::make_unique<std::thread>(&DatabaseManager::WorkerThreadFunc, this);
    
    // Connect to database
    if (!ConnectToDatabase()) {
        std::cerr << "Failed to connect to database" << std::endl;
        // Continue anyway for file-based operations
    }
    
    m_initialized = true;
    std::cout << "DatabaseManager initialized successfully" << std::endl;
    return true;
}

void DatabaseManager::Finalize() {
    if (!m_initialized) return;
    
    std::cout << "Finalizing DatabaseManager..." << std::endl;
    
    // Stop worker thread
    m_workerRunning = false;
    m_queueCondition.notify_all();
    
    if (m_workerThread && m_workerThread->joinable()) {
        m_workerThread->join();
    }
    
    // Disconnect from database
    DisconnectFromDatabase();
    
    // Clear cache
    ClearCache();
    
    m_initialized = false;
    std::cout << "DatabaseManager finalized" << std::endl;
}

void DatabaseManager::Update() {
    if (!m_initialized) return;
    
    // Check connection status
    if (!m_connected && GetCurrentTime() - m_lastConnectAttempt > 30000) {
        // Try to reconnect every 30 seconds
        ReconnectToDatabase();
    }
}

// =============== IDatabaseProvider Implementation ===============
bool DatabaseManager::LoadCharacter(const std::string& account, const std::string& charName, CharacterData& data) {
    // Try cache first
    std::string cacheKey = GetCacheKey(account, charName);
    if (GetFromCache(cacheKey, data)) {
        return true;
    }
    
    // Load from file
    if (LoadCharacterFromFile(account, charName, data)) {
        // Update cache
        UpdateCache(cacheKey, data);
        return true;
    }
    
    return false;
}

bool DatabaseManager::SaveCharacter(const std::string& account, const std::string& charName, const CharacterData& data) {
    // Save to file
    if (SaveCharacterToFile(account, charName, data)) {
        // Update cache
        std::string cacheKey = GetCacheKey(account, charName);
        UpdateCache(cacheKey, data);
        
        // Create backup if enabled
        if (m_config.enableBackup) {
            BackupCharacterData(account, charName);
        }
        
        return true;
    }
    
    return false;
}

bool DatabaseManager::CreateCharacter(const std::string& account, const CharacterData& data) {
    return CreateCharacterFile(account, data);
}

bool DatabaseManager::DeleteCharacter(const std::string& account, const std::string& charName) {
    return DeleteCharacterFile(account, charName);
}

bool DatabaseManager::LoadConfig(const std::string& configName, std::string& data) {
    std::string configPath = m_config.dataPath + "Config/" + configName + ".cfg";
    std::ifstream file(configPath);
    if (!file.is_open()) {
        return false;
    }
    
    std::ostringstream oss;
    oss << file.rdbuf();
    data = oss.str();
    file.close();
    
    return true;
}

bool DatabaseManager::SaveConfig(const std::string& configName, const std::string& data) {
    std::string configDir = m_config.dataPath + "Config/";
    if (!EnsureDirectoryExists(configDir)) {
        return false;
    }
    
    std::string configPath = configDir + configName + ".cfg";
    std::ofstream file(configPath);
    if (!file.is_open()) {
        return false;
    }
    
    file << data;
    file.close();
    
    return true;
}

DBStats DatabaseManager::GetStats() const {
    return m_stats;
}

bool DatabaseManager::IsConnected() const {
    return m_connected.load();
}

// =============== IEventSubscriber Implementation ===============
void DatabaseManager::OnEvent(const std::string& eventType, const EventData& data) {
    if (eventType == "PlayerLogin") {
        // Handle player login - prepare character data
    } else if (eventType == "PlayerLogout") {
        // Handle player logout - save character data
    } else if (eventType == "AutoSave") {
        // Handle auto-save event
    }
}

void DatabaseManager::SubscribeToEvents() {
    if (m_eventBus) {
        m_eventBus->Subscribe("PlayerLogin", [this](const EventData& data) {
            OnEvent("PlayerLogin", data);
        });
        m_eventBus->Subscribe("PlayerLogout", [this](const EventData& data) {
            OnEvent("PlayerLogout", data);
        });
        m_eventBus->Subscribe("AutoSave", [this](const EventData& data) {
            OnEvent("AutoSave", data);
        });
    }
}

void DatabaseManager::UnsubscribeFromEvents() {
    // Unsubscribe from events (EventBus implementation limitation, leave empty for now)
}

// =============== DatabaseManager Specific Functionality ===============
void DatabaseManager::LoadCharacterAsync(const std::string& account, const std::string& charName,
                       std::function<void(DBResult, const CharacterData&)> callback) {
    auto request = std::make_unique<DBRequest>();
    request->type = DBOperationType::LOAD_CHARACTER;
    request->accountName = account;
    request->characterName = charName;
    request->requestId = m_nextRequestId++;
    request->timestamp = GetCurrentTime();
    
    // Convert callback to generic form
    request->callback = [callback](DBResult result, const std::string& data) {
        CharacterData charData;
        if (result == DBResult::SUCCESS) {
            // Decode character data (simplified)
            // In real implementation, decode from data string
        }
        callback(result, charData);
    };
    
    // Add to queue
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        m_requestQueue.push(std::move(request));
        m_stats.pendingRequests++;
    }
    m_queueCondition.notify_one();
}

void DatabaseManager::SaveCharacterAsync(const std::string& account, const std::string& charName,
                       const CharacterData& data,
                       std::function<void(DBResult)> callback) {
    auto request = std::make_unique<DBRequest>();
    request->type = DBOperationType::SAVE_CHARACTER;
    request->accountName = account;
    request->characterName = charName;
    request->data = EncodeCharacterData(data);
    request->requestId = m_nextRequestId++;
    request->timestamp = GetCurrentTime();
    
    // Convert callback to generic form
    request->callback = [callback](DBResult result, const std::string&) {
        callback(result);
    };
    
    // Add to queue
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        m_requestQueue.push(std::move(request));
        m_stats.pendingRequests++;
    }
    m_queueCondition.notify_one();
}

bool DatabaseManager::LoadDBConfig(const std::string& configFile) {
    // Load configuration from file (simplified implementation)
    std::ifstream file(configFile);
    if (!file.is_open()) {
        std::cout << "Using default database configuration" << std::endl;
        return true; // Use defaults
    }
    
    // Parse configuration file (simplified)
    std::string line;
    while (std::getline(file, line)) {
        if (line.find("DataPath=") == 0) {
            m_config.dataPath = line.substr(9);
        } else if (line.find("HumPath=") == 0) {
            m_config.humPath = line.substr(8);
        } else if (line.find("BackupPath=") == 0) {
            m_config.backupPath = line.substr(11);
        }
    }
    
    file.close();
    return true;
}

void DatabaseManager::SetDBConfig(const DBConfig& config) {
    m_config = config;
}

const DBConfig& DatabaseManager::GetDBConfig() const {
    return m_config;
}

bool DatabaseManager::ConnectToDatabase() {
    m_lastConnectAttempt = GetCurrentTime();
    
    // For file-based database, just check if directories exist
    if (EnsureDirectoryExists(m_config.dataPath) &&
        EnsureDirectoryExists(m_config.humPath)) {
        m_connected = true;
        std::cout << "Connected to file-based database" << std::endl;
        return true;
    }
    
    m_connected = false;
    m_stats.connectionErrors++;
    return false;
}

void DatabaseManager::DisconnectFromDatabase() {
    m_connected = false;
    std::cout << "Disconnected from database" << std::endl;
}

bool DatabaseManager::ReconnectToDatabase() {
    std::cout << "Attempting to reconnect to database..." << std::endl;
    return ConnectToDatabase();
}

bool DatabaseManager::BackupCharacterData(const std::string& account, const std::string& charName) {
    std::string sourcePath = GetCharacterFilePath(account, charName);
    std::string backupPath = GetBackupPath(account, charName);
    
    return CreateBackup(sourcePath, backupPath);
}

bool DatabaseManager::RestoreCharacterData(const std::string& account, const std::string& charName) {
    std::string backupPath = GetBackupPath(account, charName);
    std::string targetPath = GetCharacterFilePath(account, charName);
    
    return CreateBackup(backupPath, targetPath);
}

std::vector<std::string> DatabaseManager::GetCharacterList(const std::string& account) {
    std::vector<std::string> characters;
    std::string accountPath = GetAccountPath(account);
    
    try {
        if (std::filesystem::exists(accountPath)) {
            for (const auto& entry : std::filesystem::directory_iterator(accountPath)) {
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().filename().string();
                    if (filename.find(".chr") != std::string::npos) {
                        // Extract character name from filename
                        std::string charName = filename.substr(0, filename.find_last_of("."));
                        characters.push_back(charName);
                    }
                }
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "Error listing characters: " << e.what() << std::endl;
    }
    
    return characters;
}

bool DatabaseManager::DeleteAccount(const std::string& account) {
    std::string accountPath = GetAccountPath(account);
    
    try {
        if (std::filesystem::exists(accountPath)) {
            std::filesystem::remove_all(accountPath);
            return true;
        }
    } catch (const std::exception& e) {
        std::cerr << "Error deleting account: " << e.what() << std::endl;
    }
    
    return false;
}

void DatabaseManager::CompactDatabase() {
    std::cout << "Compacting database..." << std::endl;
    // Implementation for database compaction
}

void DatabaseManager::RepairDatabase() {
    std::cout << "Repairing database..." << std::endl;
    // Implementation for database repair
}

void DatabaseManager::OptimizeDatabase() {
    std::cout << "Optimizing database..." << std::endl;
    // Implementation for database optimization
}

void DatabaseManager::SetEventBus(std::shared_ptr<EventBus> eventBus) {
    m_eventBus = eventBus;
    
    // Subscribe to events
    if (m_eventBus) {
        SubscribeToEvents();
    }
}

// =============== Private Methods Implementation ===============
void DatabaseManager::WorkerThreadFunc() {
    while (m_workerRunning.load()) {
        std::unique_lock<std::mutex> lock(m_queueMutex);
        
        // Wait for requests or stop signal
        m_queueCondition.wait(lock, [this] {
            return !m_requestQueue.empty() || !m_workerRunning.load();
        });
        
        // Process requests
        while (!m_requestQueue.empty() && m_workerRunning.load()) {
            auto request = std::move(m_requestQueue.front());
            m_requestQueue.pop();
            m_stats.pendingRequests--;
            
            // Release lock to process request
            lock.unlock();
            ProcessRequest(*request);
            lock.lock();
        }
    }
}

void DatabaseManager::ProcessRequest(const DBRequest& request) {
    DWORD startTime = GetCurrentTime();
    DBResult result = DBResult::FAILED;
    std::string responseData;
    
    m_stats.totalRequests++;
    
    try {
        switch (request.type) {
            case DBOperationType::LOAD_CHARACTER: {
                CharacterData data;
                if (LoadCharacterFromFile(request.accountName, request.characterName, data)) {
                    responseData = EncodeCharacterData(data);
                    result = DBResult::SUCCESS;
                } else {
                    result = DBResult::NOT_FOUND;
                }
                break;
            }
            case DBOperationType::SAVE_CHARACTER: {
                CharacterData data;
                if (DecodeCharacterData(request.data, data)) {
                    if (SaveCharacterToFile(request.accountName, request.characterName, data)) {
                        result = DBResult::SUCCESS;
                    }
                }
                break;
            }
            case DBOperationType::CREATE_CHARACTER: {
                CharacterData data;
                if (DecodeCharacterData(request.data, data)) {
                    if (CreateCharacterFile(request.accountName, data)) {
                        result = DBResult::SUCCESS;
                    } else {
                        result = DBResult::ALREADY_EXISTS;
                    }
                }
                break;
            }
            case DBOperationType::DELETE_CHARACTER: {
                if (DeleteCharacterFile(request.accountName, request.characterName)) {
                    result = DBResult::SUCCESS;
                }
                break;
            }
            default:
                result = DBResult::FAILED;
                break;
        }
    } catch (const std::exception& e) {
        HandleError("ProcessRequest", e.what());
        result = DBResult::FAILED;
    }
    
    // Update statistics
    DWORD responseTime = GetCurrentTime() - startTime;
    UpdateStats(result, responseTime);
    
    // Call callback if provided
    if (request.callback) {
        try {
            request.callback(result, responseData);
        } catch (const std::exception& e) {
            HandleError("Callback", e.what());
        }
    }
}

bool DatabaseManager::LoadCharacterFromFile(const std::string& account, const std::string& charName, CharacterData& data) {
    std::string filePath = GetCharacterFilePath(account, charName);
    std::ifstream file(filePath, std::ios::binary);
    
    if (!file.is_open()) {
        return false;
    }
    
    try {
        // Read character data (simplified binary format matching original)
        file.read(reinterpret_cast<char*>(&data.job), sizeof(data.job));
        file.read(reinterpret_cast<char*>(&data.hair), sizeof(data.hair));
        file.read(reinterpret_cast<char*>(&data.level), sizeof(data.level));
        file.read(reinterpret_cast<char*>(&data.exp), sizeof(data.exp));
        file.read(reinterpret_cast<char*>(&data.direction), sizeof(data.direction));
        file.read(reinterpret_cast<char*>(&data.currentX), sizeof(data.currentX));
        file.read(reinterpret_cast<char*>(&data.currentY), sizeof(data.currentY));
        
        // Read string data (map name)
        size_t mapNameSize;
        file.read(reinterpret_cast<char*>(&mapNameSize), sizeof(mapNameSize));
        data.mapName.resize(mapNameSize);
        file.read(&data.mapName[0], mapNameSize);
        
        // Read remaining numeric data
        file.read(reinterpret_cast<char*>(&data.hp), sizeof(data.hp));
        file.read(reinterpret_cast<char*>(&data.mp), sizeof(data.mp));
        file.read(reinterpret_cast<char*>(&data.maxHp), sizeof(data.maxHp));
        file.read(reinterpret_cast<char*>(&data.maxMp), sizeof(data.maxMp));
        
        // Set account and character names
        data.accountName = account;
        data.characterName = charName;
        data.lastLoginTime = GetCurrentTime();
        
        file.close();
        return true;
    } catch (const std::exception& e) {
        HandleError("LoadCharacterFromFile", e.what());
        file.close();
        return false;
    }
}

bool DatabaseManager::SaveCharacterToFile(const std::string& account, const std::string& charName, const CharacterData& data) {
    std::string accountPath = GetAccountPath(account);
    if (!EnsureDirectoryExists(accountPath)) {
        return false;
    }
    
    std::string filePath = GetCharacterFilePath(account, charName);
    std::ofstream file(filePath, std::ios::binary);
    
    if (!file.is_open()) {
        return false;
    }
    
    try {
        // Write character data (simplified binary format matching original)
        file.write(reinterpret_cast<const char*>(&data.job), sizeof(data.job));
        file.write(reinterpret_cast<const char*>(&data.hair), sizeof(data.hair));
        file.write(reinterpret_cast<const char*>(&data.level), sizeof(data.level));
        file.write(reinterpret_cast<const char*>(&data.exp), sizeof(data.exp));
        file.write(reinterpret_cast<const char*>(&data.direction), sizeof(data.direction));
        file.write(reinterpret_cast<const char*>(&data.currentX), sizeof(data.currentX));
        file.write(reinterpret_cast<const char*>(&data.currentY), sizeof(data.currentY));
        
        // Write string data (map name)
        size_t mapNameSize = data.mapName.size();
        file.write(reinterpret_cast<const char*>(&mapNameSize), sizeof(mapNameSize));
        file.write(data.mapName.c_str(), mapNameSize);
        
        // Write remaining numeric data
        file.write(reinterpret_cast<const char*>(&data.hp), sizeof(data.hp));
        file.write(reinterpret_cast<const char*>(&data.mp), sizeof(data.mp));
        file.write(reinterpret_cast<const char*>(&data.maxHp), sizeof(data.maxHp));
        file.write(reinterpret_cast<const char*>(&data.maxMp), sizeof(data.maxMp));
        
        file.close();
        return true;
    } catch (const std::exception& e) {
        HandleError("SaveCharacterToFile", e.what());
        file.close();
        return false;
    }
}

bool DatabaseManager::CreateCharacterFile(const std::string& account, const CharacterData& data) {
    // Check if character already exists
    std::string filePath = GetCharacterFilePath(account, data.characterName);
    if (std::filesystem::exists(filePath)) {
        return false; // Character already exists
    }
    
    return SaveCharacterToFile(account, data.characterName, data);
}

bool DatabaseManager::DeleteCharacterFile(const std::string& account, const std::string& charName) {
    std::string filePath = GetCharacterFilePath(account, charName);
    
    try {
        if (std::filesystem::exists(filePath)) {
            std::filesystem::remove(filePath);
            
            // Remove from cache
            std::string cacheKey = GetCacheKey(account, charName);
            std::lock_guard<std::mutex> lock(m_cacheMutex);
            m_characterCache.erase(cacheKey);
            
            return true;
        }
    } catch (const std::exception& e) {
        HandleError("DeleteCharacterFile", e.what());
    }
    
    return false;
}

std::string DatabaseManager::GetCharacterFilePath(const std::string& account, const std::string& charName) const {
    return GetAccountPath(account) + charName + ".chr";
}

std::string DatabaseManager::GetAccountPath(const std::string& account) const {
    return m_config.humPath + account + "/";
}

std::string DatabaseManager::EncodeCharacterData(const CharacterData& data) const {
    // Simple encoding - in real implementation, use proper serialization
    std::ostringstream oss;
    oss << data.accountName << "|" << data.characterName << "|" << static_cast<int>(data.job) << "|"
        << static_cast<int>(data.hair) << "|" << static_cast<int>(data.level) << "|" << data.exp;
    return oss.str();
}

bool DatabaseManager::DecodeCharacterData(const std::string& data, CharacterData& character) const {
    // Simple decoding - in real implementation, use proper deserialization
    std::istringstream iss(data);
    std::string token;
    
    if (!std::getline(iss, character.accountName, '|')) return false;
    if (!std::getline(iss, character.characterName, '|')) return false;
    
    if (std::getline(iss, token, '|')) {
        character.job = static_cast<BYTE>(std::stoi(token));
    } else {
        return false;
    }
    
    // Continue decoding other fields...
    return true;
}

void DatabaseManager::UpdateCache(const std::string& key, const CharacterData& data) {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    m_characterCache[key] = data;
}

bool DatabaseManager::GetFromCache(const std::string& key, CharacterData& data) const {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    auto it = m_characterCache.find(key);
    if (it != m_characterCache.end()) {
        data = it->second;
        return true;
    }
    return false;
}

void DatabaseManager::ClearCache() {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    m_characterCache.clear();
}

void DatabaseManager::HandleError(const std::string& operation, const std::string& error) {
    std::cerr << "DatabaseManager Error in " << operation << ": " << error << std::endl;
    // Log error to file if needed
}

std::string DatabaseManager::GetBackupPath(const std::string& account, const std::string& charName) const {
    return m_config.backupPath + account + "/" + charName + "_" + std::to_string(GetCurrentTime()) + ".bak";
}

bool DatabaseManager::CreateBackup(const std::string& source, const std::string& backup) {
    try {
        std::string backupDir = backup.substr(0, backup.find_last_of("/\\"));
        if (!EnsureDirectoryExists(backupDir)) {
            return false;
        }
        
        std::filesystem::copy_file(source, backup, std::filesystem::copy_options::overwrite_existing);
        return true;
    } catch (const std::exception& e) {
        HandleError("CreateBackup", e.what());
        return false;
    }
}

bool DatabaseManager::EnsureDirectoryExists(const std::string& path) const {
    try {
        if (!std::filesystem::exists(path)) {
            std::filesystem::create_directories(path);
        }
        return std::filesystem::exists(path);
    } catch (const std::exception& e) {
        std::cerr << "Error creating directory " << path << ": " << e.what() << std::endl;
        return false;
    }
}

void DatabaseManager::UpdateStats(DBResult result, DWORD responseTime) {
    if (result == DBResult::SUCCESS) {
        m_stats.successfulRequests++;
    } else {
        m_stats.failedRequests++;
    }
    
    // Update average response time (simple moving average)
    DWORD currentAvg = m_stats.averageResponseTime.load();
    DWORD newAvg = (currentAvg + responseTime) / 2;
    m_stats.averageResponseTime = newAvg;
}

bool DatabaseManager::UpdateCharacterCache(const std::string& charName, const CharacterData& data) {
    if (charName.empty()) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    m_characterCache[charName] = data;
    return true;
}

bool DatabaseManager::ClearCharacterCache() {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    m_characterCache.clear();
    return true;
}

bool DatabaseManager::IsCharacterCached(const std::string& charName) const {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    return m_characterCache.find(charName) != m_characterCache.end();
}

bool DatabaseManager::RemoveCharacterFromCache(const std::string& charName) {
    std::lock_guard<std::mutex> lock(m_cacheMutex);
    return m_characterCache.erase(charName) > 0;
}

} // namespace Mir200 